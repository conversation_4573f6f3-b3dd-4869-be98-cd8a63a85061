clientid = **********
access_token = 'eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************.k1XdkPy2vPPgIXLwUEwE--7da_koU9Zzkm5dM3R-3p_xrtURSRCf291PUpGSREV6VIsudGbz126lySi_-VYdGA'

from dhanhq import DhanContext, dhanhq

dhan_context = DhanContext("client_id","access_token")
dhan = dhanhq(dhan_context)



# Place an order for Equity Cash
dhan.place_order(security_id='1333',            # HDFC Bank
    exchange_segment=dhan.NSE,
    transaction_type=dhan.BUY,
    quantity=10,
    order_type=dhan.MARKET,
    product_type=dhan.INTRA,
    price=0)
    
# Place an order for NSE Futures & Options
dhan.place_order(security_id='52175',           # Nifty PE
    exchange_segment=dhan.NSE_FNO,
    transaction_type=dhan.BUY,
    quantity=550,
    order_type=dhan.MARKET,
    product_type=dhan.INTRA,
    price=0)
  
# # Fetch all orders
# dhan.get_order_list()

# # Get order by id
# dhan.get_order_by_id(order_id)

# # Modify order
# dhan.modify_order(order_id, order_type, leg_name, quantity, price, trigger_price, disclosed_quantity, validity)

# # Cancel order
# dhan.cancel_order(order_id)

# # Get order by correlation id
# dhan.get_order_by_corelationID(corelationID)

# # Get Instrument List
# dhan.fetch_security_list("compact")

# # Get positions
# dhan.get_positions()

# # Get holdings
# dhan.get_holdings()

# # Intraday Minute Data 
# dhan.intraday_minute_data(security_id, exchange_segment, instrument_type, from_date, to_date)

# # Historical Daily Data
# dhan.historical_daily_data(security_id, exchange_segment, instrument_type, from_date, to_date)

# # Time Converter
# dhan.convert_to_date_time(EPOCH Date)

# # Get trade book
# dhan.get_trade_book(order_id)

# # Get trade history
# dhan.get_trade_history(from_date,to_date,page_number=0)

# # Get fund limits
# dhan.get_fund_limits()

# # Generate TPIN
# dhan.generate_tpin()

# # Enter TPIN in Form
# dhan.open_browser_for_tpin(isin='INE00IN01015',
#     qty=1,
#     exchange='NSE')

# # EDIS Status and Inquiry
# dhan.edis_inquiry()

# # Expiry List of Underlying
# dhan.expiry_list(
#     under_security_id=13,                       # Nifty
#     under_exchange_segment="IDX_I"
# )

# # Option Chain
# dhan.option_chain(
#     under_security_id=13,                       # Nifty
#     under_exchange_segment="IDX_I",
#     expiry="2024-10-31"
# )

# # Market Quote Data                     # LTP - ticker_data, OHLC - ohlc_data, Full Packet - quote_data
# dhan.ohlc_data(
#     securities = {"NSE_EQ":[1333]}
# )

# # Place Forever Order (SINGLE)
# dhan.place_forever(
#     security_id="1333",
#     exchange_segment= dhan.NSE,
#     transaction_type= dhan.BUY,
#     product_type=dhan.CNC,
#     quantity= 10,
#     price= 1900,
#     trigger_Price= 1950
# )

