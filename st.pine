// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © KivancOzbilgic


//@version=4
strategy("SuperTrend Triple STRATEGY", overlay=true)

Periods1 = input(title="ATR Period 1", type=input.integer, defval=10)
src1 = input(hl2, title="Source 1")
Multiplier1 = input(title="ATR Multiplier 1", type=input.float, step=0.1, defval=3.0)
Periods2 = input(title="ATR Period 2", type=input.integer, defval=11)
src2 = input(hl2, title="Source 2")
Multiplier2 = input(title="ATR Multiplier 2", type=input.float, step=0.1, defval=2.0)
Periods3 = input(title="ATR Period 3", type=input.integer, defval=12)
src3 = input(hl2, title="Source 3")
Multiplier3 = input(title="ATR Multiplier 3", type=input.float, step=0.1, defval=1.0)
changeATR= input(title="Change ATR Calculation Method ?", type=input.bool, defval=true)

showsignals = input(title="Show Buy/Sell Signals ?", type=input.bool, defval=false)
highlighting = input(title="Highlighter On/Off ?", type=input.bool, defval=true)
barcoloring = input(title="Bar Coloring On/Off ?", type=input.bool, defval=true)

// First SuperTrend calculation
atr1_2 = sma(tr, Periods1)
atr1 = changeATR ? atr(Periods1) : atr1_2
up1 = src1-(Multiplier1*atr1)
up1_1 = nz(up1[1], up1)
up1 := close[1] > up1_1 ? max(up1, up1_1) : up1
dn1 = src1+(Multiplier1*atr1)
dn1_1 = nz(dn1[1], dn1)
dn1 := close[1] < dn1_1 ? min(dn1, dn1_1) : dn1
trend1 = 1
trend1 := nz(trend1[1], trend1)
trend1 := trend1 == -1 and close > dn1_1 ? 1 : trend1 == 1 and close < up1_1 ? -1 : trend1

// Second SuperTrend calculation
atr2_2 = sma(tr, Periods2)
atr2 = changeATR ? atr(Periods2) : atr2_2
up2 = src2-(Multiplier2*atr2)
up2_1 = nz(up2[1], up2)
up2 := close[1] > up2_1 ? max(up2, up2_1) : up2
dn2 = src2+(Multiplier2*atr2)
dn2_1 = nz(dn2[1], dn2)
dn2 := close[1] < dn2_1 ? min(dn2, dn2_1) : dn2
trend2 = 1
trend2 := nz(trend2[1], trend2)
trend2 := trend2 == -1 and close > dn2_1 ? 1 : trend2 == 1 and close < up2_1 ? -1 : trend2

// Third SuperTrend calculation
atr3_2 = sma(tr, Periods3)
atr3 = changeATR ? atr(Periods3) : atr3_2
up3 = src3-(Multiplier3*atr3)
up3_1 = nz(up3[1], up3)
up3 := close[1] > up3_1 ? max(up3, up3_1) : up3
dn3 = src3+(Multiplier3*atr3)
dn3_1 = nz(dn3[1], dn3)
dn3 := close[1] < dn3_1 ? min(dn3, dn3_1) : dn3
trend3 = 1
trend3 := nz(trend3[1], trend3)
trend3 := trend3 == -1 and close > dn3_1 ? 1 : trend3 == 1 and close < up3_1 ? -1 : trend3

// Plot SuperTrend 1
upPlot1 = plot(trend1 == 1 ? up1 : na, title="Up Trend 1", style=plot.style_linebr, linewidth=2, color=color.green)
dnPlot1 = plot(trend1 == -1 ? dn1 : na, title="Down Trend 1", style=plot.style_linebr, linewidth=2, color=color.red)

// Plot SuperTrend 2
upPlot2 = plot(trend2 == 1 ? up2 : na, title="Up Trend 2", style=plot.style_linebr, linewidth=1, color=color.lime)
dnPlot2 = plot(trend2 == -1 ? dn2 : na, title="Down Trend 2", style=plot.style_linebr, linewidth=1, color=color.orange)

// Plot SuperTrend 3
upPlot3 = plot(trend3 == 1 ? up3 : na, title="Up Trend 3", style=plot.style_linebr, linewidth=1, color=color.blue)
dnPlot3 = plot(trend3 == -1 ? dn3 : na, title="Down Trend 3", style=plot.style_linebr, linewidth=1, color=color.purple)

// Combined signal - All three trends must align
buySignal = trend1 == 1 and trend1[1] == -1 and trend2 == 1 and trend3 == 1
sellSignal = trend1 == -1 and trend1[1] == 1 and trend2 == -1 and trend3 == -1

// Display signals
plotshape(buySignal ? up1 : na, title="UpTrend Begins", location=location.absolute, style=shape.circle, size=size.tiny, color=color.green, transp=0)
plotshape(buySignal and showsignals ? up1 : na, title="Buy", text="Buy", location=location.absolute, style=shape.labelup, size=size.tiny, color=color.green, textcolor=color.white, transp=0)

plotshape(sellSignal ? dn1 : na, title="DownTrend Begins", location=location.absolute, style=shape.circle, size=size.tiny, color=color.red, transp=0)
plotshape(sellSignal and showsignals ? dn1 : na, title="Sell", text="Sell", location=location.absolute, style=shape.labeldown, size=size.tiny, color=color.red, textcolor=color.white, transp=0)

mPlot = plot(ohlc4, title="", style=plot.style_circles, linewidth=0)
longFillColor = highlighting ? (trend1 == 1 and trend2 == 1 and trend3 == 1 ? color.green : color.white) : color.white
shortFillColor = highlighting ? (trend1 == -1 and trend2 == -1 and trend3 == -1 ? color.red : color.white) : color.white
fill(mPlot, upPlot1, title="UpTrend Highlighter", color=longFillColor)
fill(mPlot, dnPlot1, title="DownTrend Highlighter", color=shortFillColor)

FromMonth = input(defval = 9, title = "From Month", minval = 1, maxval = 12)
FromDay   = input(defval = 1, title = "From Day", minval = 1, maxval = 31)
FromYear  = input(defval = 2018, title = "From Year", minval = 999)
ToMonth   = input(defval = 1, title = "To Month", minval = 1, maxval = 12)
ToDay     = input(defval = 1, title = "To Day", minval = 1, maxval = 31)
ToYear    = input(defval = 9999, title = "To Year", minval = 999)
start     = timestamp(FromYear, FromMonth, FromDay, 00, 00)  
finish    = timestamp(ToYear, ToMonth, ToDay, 23, 59)       
window()  => time >= start and time <= finish ? true : false

// Entry strategy - only enter when all three SuperTrends align
longCondition = buySignal
if (longCondition)
    strategy.entry("BUY", strategy.long, when = window())
    
shortCondition = sellSignal
if (shortCondition)
    strategy.entry("SELL", strategy.short, when = window())
    
buy1= barssince(buySignal)
sell1 = barssince(sellSignal)
color1 = buy1[1] < sell1[1] ? color.green : buy1[1] > sell1[1] ? color.red : na
barcolor(barcoloring ? color1 : na)